# 方案清单管理系统

一个基于单HTML文件的智能化解决方案物料清单管理与维护平台。

## 功能特性

### 1. 方案管理
- **方案选择**: 支持从多个Excel方案文件中选择和加载
- **层级展示**: 按照4级层级结构展示方案内容
  - 级别1: 方案总体配置（高配/低配）
  - 级别2: 主要功能模块（作为页签展示）
  - 级别3: 子功能分类
  - 级别4: 具体产品物料

### 2. 数据展示
- **页签式界面**: 级别2作为页签，便于快速切换不同功能模块
- **分类展示**: 级别3作为可折叠的分类，级别4产品在分类下展示
- **产品信息**: 显示产品编号、描述、数量、生命周期状态等
- **生命周期标识**: 用不同颜色标识产品的生命周期状态（量产、开发、即将停售、废弃）

### 3. 产品维护
- **编辑产品**: 修改产品描述、生命周期状态、备注等信息
- **替换产品**: 支持单个替换或批量替换产品编号
- **插入产品**: 在指定位置向上或向下插入新产品
- **删除产品**: 删除不需要的产品物料
- **生命周期筛选**: 按生命周期状态筛选显示产品

### 4. 数据导出
- **方案清单导出**: 导出完整的方案物料清单为CSV格式
- **变更记录导出**: 导出所有维护操作的变更记录

### 5. 统计信息
- 显示总产品数、分类数、级别4产品数等统计信息

## 文件结构

```
├── solution_manager.html      # 主要的单HTML文件应用
├── convert_to_json.js        # Excel转JSON转换脚本
├── server.js                 # 本地HTTP服务器
├── solutions_simplified.json # 简化的方案数据（用于展示）
├── solutions_data.json       # 完整的方案数据
├── data/                     # 原始Excel数据文件夹
│   ├── M200701.00024.xlsx   # 方案文件
│   ├── M200701.00025.xlsx
│   └── ...
└── README.md                # 说明文档
```

## 使用方法

### 1. 环境准备
确保已安装Node.js，然后安装依赖：
```bash
npm install xlsx
```

### 2. 数据转换
将Excel文件转换为JSON格式：
```bash
node convert_to_json.js
```

### 3. 启动服务器
启动本地HTTP服务器：
```bash
node server.js
```

### 4. 访问系统
在浏览器中访问：http://localhost:3000

## 操作指南

### 加载方案
1. 在"选择方案"下拉框中选择要查看的方案
2. 点击"加载方案"按钮
3. 系统将显示方案的层级结构和统计信息

### 浏览方案内容
1. 点击不同的页签查看各功能模块
2. 点击分类标题展开/收起分类内容
3. 查看产品的详细信息和生命周期状态

### 维护产品数据
1. **编辑产品**: 点击产品右侧的"编辑"按钮，在弹出窗口中修改信息
2. **替换产品**: 点击"替换"按钮，输入新的产品编号
3. **插入产品**: 点击"向上插入"或"向下插入"，添加新产品
4. **删除产品**: 在编辑窗口中点击"删除"按钮

### 筛选和导出
1. **生命周期筛选**: 选择特定的生命周期状态进行筛选
2. **导出方案**: 点击"导出方案"下载完整的方案清单CSV文件
3. **导出变更**: 点击"导出变更记录"下载维护操作记录

## 数据格式说明

### Excel文件要求
- 文件名格式: `M*.xlsx`（如M200701.00024.xlsx）
- 必须包含BOM清单部分，包含以下关键列：
  - `级别`: 层级深度（1-4）
  - `物件编号`: 唯一标识符
  - `物件描述`: 可读名称
  - `数量`: 使用数量
  - `物件的生命周期阶段`: 生命周期状态
  - `备注`: 备注信息

### 产品编号规则
- **分类节点**: 以`X08.`开头（如X08.01.01002）
- **实体产品**: 以数字开头（如1.0.01.04.39733）

## 技术特性

- **单文件应用**: 所有功能集成在一个HTML文件中
- **响应式设计**: 支持桌面和移动设备
- **本地存储**: 变更记录保存在浏览器本地
- **实时更新**: 修改后立即更新显示
- **CSV导出**: 支持Excel兼容的CSV格式导出

## 注意事项

1. **数据安全**: 所有操作都在本地进行，不会上传到服务器
2. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Edge等）
3. **数据备份**: 重要修改前建议备份原始Excel文件
4. **性能考虑**: 大型方案（>1000条记录）可能影响页面响应速度

## 故障排除

### 常见问题
1. **无法加载方案列表**: 检查JSON文件是否存在，服务器是否正常运行
2. **页面显示异常**: 清除浏览器缓存，刷新页面
3. **导出功能不工作**: 检查浏览器是否允许下载文件

### 技术支持
如遇到问题，请检查：
1. Node.js版本是否兼容
2. 数据文件格式是否正确
3. 浏览器控制台是否有错误信息

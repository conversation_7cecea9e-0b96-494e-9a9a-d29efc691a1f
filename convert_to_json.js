const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// 读取Excel文件并转换为JSON
function convertExcelToJson(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // 查找BOM清单开始位置
        let bomStartRow = -1;
        let headers = [];
        
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            if (row && row.some(cell => cell === '级别' || cell === '物件编号')) {
                bomStartRow = i;
                headers = row.filter(cell => cell && cell.trim() !== '');
                break;
            }
        }

        if (bomStartRow === -1) {
            console.log(`文件 ${path.basename(filePath)} 未找到BOM清单部分`);
            return null;
        }

        // 提取BOM数据
        const bomData = [];
        for (let i = bomStartRow + 1; i < data.length; i++) {
            const row = data[i];
            if (!row || !row[0]) continue;
            
            const rowData = {};
            headers.forEach((header, index) => {
                rowData[header] = row[index] || '';
            });
            
            if (rowData['级别']) {
                bomData.push(rowData);
            }
        }

        return {
            fileName: path.basename(filePath),
            modelCode: path.basename(filePath, '.xlsx'),
            headers,
            bomData,
            totalRecords: bomData.length,
            levelStats: bomData.reduce((stats, item) => {
                const level = item['级别'];
                stats[level] = (stats[level] || 0) + 1;
                return stats;
            }, {})
        };
    } catch (error) {
        console.error(`处理文件 ${filePath} 失败:`, error.message);
        return null;
    }
}

// 主函数
function main() {
    console.log('开始转换Excel文件为JSON格式...\n');
    
    const dataDir = './data';
    const files = fs.readdirSync(dataDir);
    
    const solutionFiles = files.filter(f => f.startsWith('M') && f.endsWith('.xlsx'));
    console.log('找到方案文件:', solutionFiles);
    
    const solutions = [];
    
    solutionFiles.forEach(file => {
        const result = convertExcelToJson(path.join(dataDir, file));
        if (result) {
            solutions.push(result);
            console.log(`✓ 处理完成: ${file} (${result.totalRecords}条记录)`);
        }
    });
    
    // 生成完整的JSON数据
    const jsonData = {
        metadata: {
            generatedAt: new Date().toISOString(),
            totalSolutions: solutions.length,
            totalRecords: solutions.reduce((sum, s) => sum + s.totalRecords, 0)
        },
        solutions: solutions
    };
    
    // 保存完整数据
    fs.writeFileSync('solutions_data.json', JSON.stringify(jsonData, null, 2), 'utf8');
    console.log('\n✓ 完整数据已保存到 solutions_data.json');
    
    // 生成简化的数据用于HTML展示（只包含前50条记录）
    const simplifiedSolutions = solutions.map(solution => ({
        fileName: solution.fileName,
        modelCode: solution.modelCode,
        totalRecords: solution.totalRecords,
        levelStats: solution.levelStats,
        sampleRecords: solution.bomData.slice(0, 50) // 只取前50条作为示例
    }));
    
    const simplifiedData = {
        metadata: jsonData.metadata,
        solutions: simplifiedSolutions
    };
    
    fs.writeFileSync('solutions_simplified.json', JSON.stringify(simplifiedData, null, 2), 'utf8');
    console.log('✓ 简化数据已保存到 solutions_simplified.json');
    
    // 生成方案摘要
    console.log('\n=== 方案摘要 ===');
    solutions.forEach(solution => {
        console.log(`${solution.modelCode}:`);
        console.log(`  - 总记录数: ${solution.totalRecords}`);
        console.log(`  - 层级分布: ${JSON.stringify(solution.levelStats)}`);
        
        // 获取方案名称（级别1的描述）
        const level1Item = solution.bomData.find(item => item['级别'] === '1');
        if (level1Item) {
            console.log(`  - 方案名称: ${level1Item['物件描述']}`);
        }
        console.log('');
    });
    
    console.log('转换完成！');
}

if (require.main === module) {
    main();
}

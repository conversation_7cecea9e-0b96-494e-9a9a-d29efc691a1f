const fs = require('fs');

// 读取数据
const data = JSON.parse(fs.readFileSync('solutions_simplified.json', 'utf8'));
const solution = data.solutions.find(s => s.modelCode === 'M200701.00024');
const bomData = solution.sampleRecords;

console.log('测试层级关系 - M200701.00024 高配方案');
console.log('='.repeat(50));

// 找到高配的数据
const highConfigCode = solution.configurations.find(c => c.type === 'high')?.code;
console.log('高配编号:', highConfigCode);

// 模拟isChildOf函数
function isChildOf(childItem, parentCode, allData) {
    const childIndex = allData.findIndex(item => item['物件编号'] === childItem['物件编号']);
    const parentIndex = allData.findIndex(item => item['物件编号'] === parentCode);
    
    if (childIndex === -1 || parentIndex === -1) return false;
    if (childIndex <= parentIndex) return false;
    
    const parentLevel = parseInt(allData[parentIndex]['级别']);
    const childLevel = parseInt(childItem['级别']);
    
    // 特殊处理：如果父项和子项都是级别4，且父项是X08开头的选件
    if (parentLevel === 4 && childLevel === 4 && parentCode.startsWith('X08.')) {
        for (let i = parentIndex + 1; i < allData.length; i++) {
            if (allData[i]['物件编号'] === childItem['物件编号']) {
                return true;
            }
            
            const currentLevel = parseInt(allData[i]['级别']);
            if (currentLevel < parentLevel || 
                (currentLevel === parentLevel && allData[i]['物件编号'].startsWith('X08.'))) {
                break;
            }
        }
        return false;
    }
    
    if (childLevel <= parentLevel) return false;
    
    for (let i = parentIndex + 1; i < allData.length; i++) {
        if (allData[i]['物件编号'] === childItem['物件编号']) {
            return true;
        }
        
        const currentLevel = parseInt(allData[i]['级别']);
        if (currentLevel <= parentLevel) {
            break;
        }
    }
    
    return false;
}

// 模拟isUnderConfig函数
function isUnderConfig(item, configCode) {
    const itemIndex = bomData.findIndex(d => d['物件编号'] === item['物件编号']);
    const configIndex = bomData.findIndex(d => d['物件编号'] === configCode);
    
    if (itemIndex === -1 || configIndex === -1) return false;
    
    let nextConfigIndex = bomData.length;
    for (let i = configIndex + 1; i < bomData.length; i++) {
        if (bomData[i]['级别'] === '1') {
            nextConfigIndex = i;
            break;
        }
    }
    
    return itemIndex > configIndex && itemIndex < nextConfigIndex;
}

// 过滤高配数据
const highConfigData = bomData.filter(item => {
    if (item['级别'] === '1') {
        return item['物件编号'] === highConfigCode;
    }
    return isUnderConfig(item, highConfigCode);
});

console.log(`高配数据总数: ${highConfigData.length}`);

// 找到级别2数据
const level2Items = highConfigData.filter(item => item['级别'] === '2');
console.log(`\n级别2项目数: ${level2Items.length}`);

// 测试第一个级别2下的结构
if (level2Items.length > 0) {
    const firstLevel2 = level2Items[0];
    console.log(`\n测试级别2: ${firstLevel2['物件编号']} - ${firstLevel2['物件描述']}`);
    
    // 找到其下的级别3
    const level3Items = highConfigData.filter(item => 
        item['级别'] === '3' && isChildOf(item, firstLevel2['物件编号'], highConfigData)
    );
    
    console.log(`级别3项目数: ${level3Items.length}`);
    
    // 测试第一个级别3下的结构
    if (level3Items.length > 0) {
        const firstLevel3 = level3Items[0];
        console.log(`\n测试级别3: ${firstLevel3['物件编号']} - ${firstLevel3['物件描述']}`);
        
        // 找到其下的级别4
        const level4Items = highConfigData.filter(item => 
            item['级别'] === '4' && isChildOf(item, firstLevel3['物件编号'], highConfigData)
        );
        
        console.log(`级别4项目数: ${level4Items.length}`);
        
        // 显示级别4项目
        level4Items.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item['物件编号']} - ${item['物件描述']}`);
            
            // 如果是X08开头的选件，查找其下的产品
            if (item['物件编号'].startsWith('X08.')) {
                const subProducts = highConfigData.filter(subItem => 
                    subItem['级别'] === '4' && 
                    !subItem['物件编号'].startsWith('X08.') && 
                    isChildOf(subItem, item['物件编号'], highConfigData)
                );
                
                if (subProducts.length > 0) {
                    console.log(`    └─ 下挂产品 (${subProducts.length}个):`);
                    subProducts.forEach(product => {
                        console.log(`       • ${product['物件编号']} - ${product['物件描述']}`);
                    });
                }
            }
        });
    }
}

console.log('\n测试完成!');

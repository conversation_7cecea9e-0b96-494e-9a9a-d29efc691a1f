const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// 读取Excel文件的函数
function readExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        return data;
    } catch (error) {
        console.error(`读取文件 ${filePath} 失败:`, error.message);
        return null;
    }
}

// 分析层级化解决方案文件
function analyzeSolutionFile(filePath) {
    console.log(`\n=== 分析文件: ${path.basename(filePath)} ===`);
    const data = readExcelFile(filePath);
    if (!data) return null;

    // 查找BOM清单开始位置
    let bomStartRow = -1;
    let headers = [];
    
    for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (row && row.some(cell => cell === '级别' || cell === '物件编号')) {
            bomStartRow = i;
            headers = row.filter(cell => cell && cell.trim() !== '');
            break;
        }
    }

    if (bomStartRow === -1) {
        console.log('未找到BOM清单部分');
        return null;
    }

    console.log('找到BOM清单，表头:', headers);
    
    // 提取BOM数据
    const bomData = [];
    for (let i = bomStartRow + 1; i < data.length; i++) {
        const row = data[i];
        if (!row || !row[0]) continue; // 跳过空行
        
        const rowData = {};
        headers.forEach((header, index) => {
            rowData[header] = row[index] || '';
        });
        
        // 只保留有级别的行
        if (rowData['级别']) {
            bomData.push(rowData);
        }
    }

    console.log(`提取到 ${bomData.length} 条BOM记录`);
    
    // 分析层级结构
    const levelStats = {};
    bomData.forEach(item => {
        const level = item['级别'];
        if (!levelStats[level]) {
            levelStats[level] = 0;
        }
        levelStats[level]++;
    });

    console.log('层级统计:', levelStats);

    // 分析级别1的配置类型（高配/低配）
    const level1Items = bomData.filter(item => item['级别'] === '1');
    console.log('\n级别1配置类型:');
    level1Items.forEach((item, index) => {
        console.log(`${index + 1}. ${item['物件编号']} - ${item['物件描述']}`);
    });

    // 显示前几条记录作为示例
    console.log('\n前10条记录示例:');
    bomData.slice(0, 10).forEach((item, index) => {
        console.log(`${index + 1}. 级别${item['级别']} - ${item['物件编号']} - ${item['物件描述']}`);
    });

    return {
        fileName: path.basename(filePath),
        headers,
        bomData,
        levelStats
    };
}

// 分析扁平化产品关联文件
function analyzeProductFile(filePath) {
    console.log(`\n=== 分析文件: ${path.basename(filePath)} ===`);

    try {
        const workbook = XLSX.readFile(filePath);
        console.log('工作表列表:', workbook.SheetNames);

        // 尝试读取sheet2
        let sheetName = 'Sheet2';
        if (!workbook.SheetNames.includes(sheetName)) {
            sheetName = workbook.SheetNames[1] || workbook.SheetNames[0]; // 使用第二个或第一个sheet
        }

        console.log(`使用工作表: ${sheetName}`);
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (!data || data.length === 0) {
            console.log('工作表为空');
            return null;
        }

        // 先显示前几行来调试
        console.log('文件前10行内容:');
        data.slice(0, 10).forEach((row, index) => {
            console.log(`第${index + 1}行:`, row);
        });

        // 查找表头
        let headerRow = -1;
        let headers = [];

        for (let i = 0; i < Math.min(20, data.length); i++) {
            const row = data[i];
            if (row && (row.some(cell => cell && cell.toString().includes('模型编号')) ||
                       row.some(cell => cell && cell.toString().includes('产品编号')) ||
                       row.some(cell => cell && cell.toString().includes('选件编号')))) {
                headerRow = i;
                headers = row.filter(cell => cell && cell.toString().trim() !== '');
                break;
            }
        }

        if (headerRow === -1) {
            console.log('未找到产品数据表头，尝试使用第一行作为表头');
            if (data.length > 0 && data[0]) {
                headerRow = 0;
                headers = data[0].filter(cell => cell && cell.toString().trim() !== '');
            } else {
                return null;
            }
        }

        console.log('找到产品数据，表头:', headers);

        // 提取产品数据
        const productData = [];
        for (let i = headerRow + 1; i < data.length; i++) {
            const row = data[i];
            if (!row || !row[0]) continue;

            const rowData = {};
            headers.forEach((header, index) => {
                rowData[header] = row[index] || '';
            });

            if (rowData['模型编号']) {
                productData.push(rowData);
            }
        }

        console.log(`提取到 ${productData.length} 条产品记录`);

        // 统计模型数量
        const modelStats = {};
        productData.forEach(item => {
            const model = item['模型编号'];
            if (!modelStats[model]) {
                modelStats[model] = 0;
            }
            modelStats[model]++;
        });

        console.log('模型统计:', modelStats);

        return {
            fileName: path.basename(filePath),
            headers,
            productData,
            modelStats
        };
    } catch (error) {
        console.error(`处理产品文件失败:`, error.message);
        return null;
    }
}

// 主函数
function main() {
    console.log('开始分析数据文件...\n');
    
    const dataDir = './data';
    const files = fs.readdirSync(dataDir);
    
    const solutionFiles = files.filter(f => f.startsWith('M') && f.endsWith('.xlsx'));
    const productFiles = files.filter(f => f.includes('政府退市查询') && f.endsWith('.xlsx'));
    
    console.log('找到方案文件:', solutionFiles);
    console.log('找到产品文件:', productFiles);
    
    // 分析方案文件
    const solutionAnalysis = [];
    solutionFiles.forEach(file => {
        const result = analyzeSolutionFile(path.join(dataDir, file));
        if (result) {
            solutionAnalysis.push(result);
        }
    });
    
    // 分析产品文件
    const productAnalysis = [];
    productFiles.forEach(file => {
        const result = analyzeProductFile(path.join(dataDir, file));
        if (result) {
            productAnalysis.push(result);
        }
    });
    
    // 生成数据结构JSON
    const analysisResult = {
        solutionFiles: solutionAnalysis,
        productFiles: productAnalysis,
        timestamp: new Date().toISOString()
    };
    
    // 保存分析结果
    fs.writeFileSync('data_analysis.json', JSON.stringify(analysisResult, null, 2), 'utf8');
    console.log('\n分析结果已保存到 data_analysis.json');
    
    // 生成简化的数据样本用于HTML开发
    const sampleData = {
        solutions: solutionAnalysis.map(s => ({
            fileName: s.fileName,
            sampleRecords: s.bomData.slice(0, 10)
        })),
        products: productAnalysis.map(p => ({
            fileName: p.fileName,
            sampleRecords: p.productData.slice(0, 10)
        }))
    };
    
    fs.writeFileSync('sample_data.json', JSON.stringify(sampleData, null, 2), 'utf8');
    console.log('样本数据已保存到 sample_data.json');
}

if (require.main === module) {
    main();
}

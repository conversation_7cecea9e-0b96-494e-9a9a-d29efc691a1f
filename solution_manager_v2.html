<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案清单管理系统 v2.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .control-group label {
            font-weight: bold;
            min-width: 100px;
        }

        select, input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        select {
            min-width: 200px;
        }

        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        button:hover {
            background: #5a6fd8;
        }

        button.secondary {
            background: #6c757d;
        }

        button.secondary:hover {
            background: #5a6268;
        }

        button.success {
            background: #28a745;
        }

        button.success:hover {
            background: #218838;
        }

        button.danger {
            background: #dc3545;
        }

        button.danger:hover {
            background: #c82333;
        }

        .file-input {
            display: none;
        }

        .file-label {
            display: inline-block;
            padding: 8px 12px;
            background: #28a745;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .file-label:hover {
            background: #218838;
        }

        .config-selector {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .config-options {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .config-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-option input[type="radio"] {
            margin: 0;
        }

        .tabs {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tab-headers {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }

        .tab-header {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            white-space: nowrap;
            transition: all 0.3s;
            min-width: 150px;
            text-align: center;
        }

        .tab-header:hover {
            background: #e9ecef;
        }

        .tab-header.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: bold;
        }

        .tab-content {
            padding: 20px;
            min-height: 500px;
        }

        .category {
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .category-header {
            background: #f8f9fa;
            padding: 12px 15px;
            font-weight: bold;
            color: #495057;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-header:hover {
            background: #e9ecef;
        }

        .category-content {
            padding: 15px;
            background: white;
        }

        .product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 8px;
            background: #fafafa;
            transition: all 0.3s;
        }

        .product-item:hover {
            background: #f0f8ff;
            border-color: #667eea;
        }

        .product-info {
            flex: 1;
        }

        .product-code {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .product-desc {
            color: #666;
            font-size: 14px;
        }

        .product-meta {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        .product-actions {
            display: flex;
            gap: 8px;
        }

        .product-actions button {
            padding: 5px 10px;
            font-size: 12px;
        }

        .lifecycle-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        .lifecycle-量产 { background: #d4edda; color: #155724; }
        .lifecycle-开发 { background: #fff3cd; color: #856404; }
        .lifecycle-即将停售 { background: #f8d7da; color: #721c24; }
        .lifecycle-废弃 { background: #f5f5f5; color: #6c757d; }
        .lifecycle-工程样机 { background: #d1ecf1; color: #0c5460; }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .empty {
            text-align: center;
            padding: 50px;
            color: #999;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            cursor: pointer;
            font-size: 24px;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
        }

        .notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
            color: #856404;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group label {
                min-width: auto;
            }
            
            .stats {
                flex-direction: column;
            }
            
            .product-item {
                flex-direction: column;
                align-items: stretch;
            }
            
            .product-actions {
                margin-top: 10px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>方案清单管理系统 v2.0</h1>
            <p>智能化解决方案物料清单管理与维护平台 - 支持数据导入导出</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>数据导入:</label>
                <label for="excelFile" class="file-label">选择Excel文件</label>
                <input type="file" id="excelFile" class="file-input" accept=".xlsx,.xls" multiple>
                <button onclick="importData()" class="success">导入数据</button>
                <button onclick="loadSampleData()" class="secondary">加载示例数据</button>
            </div>
            
            <div class="control-group">
                <label>选择方案:</label>
                <select id="solutionSelect">
                    <option value="">请选择方案...</option>
                </select>
                <button onclick="loadSolution()">加载方案</button>
                <button onclick="refreshData()" class="secondary">刷新数据</button>
            </div>
        </div>

        <div class="config-selector" id="configSelector" style="display: none;">
            <label><strong>配置选择:</strong></label>
            <div class="config-options" id="configOptions">
                <!-- 动态生成配置选项 -->
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>生命周期筛选:</label>
                <select id="lifecycleFilter">
                    <option value="">全部</option>
                    <option value="量产">量产</option>
                    <option value="开发">开发</option>
                    <option value="即将停售">即将停售</option>
                    <option value="废弃">废弃</option>
                    <option value="工程样机">工程样机</option>
                </select>
                <button onclick="applyFilter()">应用筛选</button>
                <button onclick="exportSolution()" class="secondary">导出方案</button>
                <button onclick="exportChanges()" class="secondary">导出变更记录</button>
                <button onclick="exportToExcel()" class="success">导出Excel</button>
            </div>
        </div>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">总产品数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">分类数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="level4Products">0</div>
                <div class="stat-label">级别4产品</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="currentConfig">-</div>
                <div class="stat-label">当前配置</div>
            </div>
        </div>

        <div class="tabs" id="solutionTabs" style="display: none;">
            <div class="tab-headers" id="tabHeaders">
                <!-- 动态生成页签 -->
            </div>
            <div class="tab-content" id="tabContent">
                <div class="loading">请选择方案并加载数据</div>
            </div>
        </div>
    </div>

    <!-- 产品编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑产品</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="editForm">
                <div class="form-group">
                    <label>产品编号:</label>
                    <input type="text" id="editProductCode" readonly>
                </div>
                <div class="form-group">
                    <label>产品描述:</label>
                    <input type="text" id="editProductDesc">
                </div>
                <div class="form-group">
                    <label>生命周期:</label>
                    <select id="editLifecycle">
                        <option value="量产">量产</option>
                        <option value="开发">开发</option>
                        <option value="即将停售">即将停售</option>
                        <option value="废弃">废弃</option>
                        <option value="工程样机">工程样机</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>备注:</label>
                    <input type="text" id="editRemark">
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" onclick="closeModal()" class="secondary">取消</button>
                    <button type="button" onclick="saveProduct()">保存</button>
                    <button type="button" onclick="deleteProduct()" class="danger">删除</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入XLSX库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <script>
        // 全局变量
        let currentSolutionData = null;
        let currentSolution = '';
        let currentConfig = 'high'; // 默认高配
        let filteredData = null;
        let changeLog = [];
        let allSolutionsData = {};
        let productRefreshData = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadSampleData(); // 自动加载示例数据
        });

        function setupEventListeners() {
            document.getElementById('lifecycleFilter').addEventListener('change', applyFilter);
            document.getElementById('excelFile').addEventListener('change', handleFileSelect);
        }

        // 加载示例数据
        async function loadSampleData() {
            try {
                const response = await fetch('solutions_simplified.json');
                const data = await response.json();

                allSolutionsData = data;
                populateSolutionSelect(data.solutions);

                if (data.productData) {
                    productRefreshData = data.productData;
                    console.log('产品刷新数据已加载:', productRefreshData.totalRecords, '条记录');
                }

            } catch (error) {
                console.error('加载示例数据失败:', error);
                // 如果加载失败，显示文件导入提示
                showNotice('未找到示例数据文件，请导入Excel文件或确保数据文件存在');
            }
        }

        // 填充方案选择下拉框
        function populateSolutionSelect(solutions) {
            const select = document.getElementById('solutionSelect');
            select.innerHTML = '<option value="">请选择方案...</option>';

            solutions.forEach(solution => {
                const option = document.createElement('option');
                option.value = solution.modelCode;

                // 获取方案名称
                const highConfig = solution.configurations.find(c => c.type === 'high');
                const solutionName = highConfig ? highConfig.name.replace('高配-', '') : solution.modelCode;

                option.textContent = `${solution.modelCode} - ${solutionName} (${solution.totalRecords}条记录)`;
                select.appendChild(option);
            });
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                console.log('选择了', files.length, '个文件');
                Array.from(files).forEach(file => {
                    console.log('文件:', file.name, '大小:', file.size, 'bytes');
                });
            }
        }

        // 导入数据
        async function importData() {
            const files = document.getElementById('excelFile').files;
            if (files.length === 0) {
                alert('请先选择Excel文件');
                return;
            }

            try {
                showNotice('正在导入数据，请稍候...');

                const importedData = {
                    solutions: [],
                    productData: null
                };

                for (let file of files) {
                    if (file.name.startsWith('M') && file.name.endsWith('.xlsx')) {
                        // 方案文件
                        const solutionData = await parseExcelFile(file, 'solution');
                        if (solutionData) {
                            importedData.solutions.push(solutionData);
                        }
                    } else if (file.name.includes('政府退市查询') && file.name.endsWith('.xlsx')) {
                        // 产品刷新文件
                        const productData = await parseExcelFile(file, 'product');
                        if (productData) {
                            importedData.productData = productData;
                        }
                    }
                }

                if (importedData.solutions.length > 0 || importedData.productData) {
                    allSolutionsData = {
                        metadata: {
                            generatedAt: new Date().toISOString(),
                            source: 'imported'
                        },
                        solutions: importedData.solutions,
                        productData: importedData.productData
                    };

                    populateSolutionSelect(importedData.solutions);

                    if (importedData.productData) {
                        productRefreshData = importedData.productData;
                    }

                    showNotice(`导入成功！方案: ${importedData.solutions.length}个，产品数据: ${importedData.productData ? '已导入' : '未导入'}`, 'success');
                } else {
                    showNotice('未找到有效的数据文件', 'error');
                }

            } catch (error) {
                console.error('导入数据失败:', error);
                showNotice('导入数据失败: ' + error.message, 'error');
            }
        }

        // 解析Excel文件
        async function parseExcelFile(file, type) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = XLSX.read(data, { type: 'array' });

                        if (type === 'solution') {
                            resolve(parseSolutionWorkbook(workbook, file.name));
                        } else if (type === 'product') {
                            resolve(parseProductWorkbook(workbook, file.name));
                        }
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(file);
            });
        }

        // 解析方案工作簿
        function parseSolutionWorkbook(workbook, fileName) {
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // 查找BOM清单开始位置
            let bomStartRow = -1;
            let headers = [];

            for (let i = 0; i < data.length; i++) {
                const row = data[i];
                if (row && row.some(cell => cell === '级别' || cell === '物件编号')) {
                    bomStartRow = i;
                    headers = row.filter(cell => cell && cell.toString().trim() !== '');
                    break;
                }
            }

            if (bomStartRow === -1) {
                throw new Error(`文件 ${fileName} 未找到BOM清单部分`);
            }

            // 提取BOM数据
            const bomData = [];
            for (let i = bomStartRow + 1; i < data.length; i++) {
                const row = data[i];
                if (!row || !row[0]) continue;

                const rowData = {};
                headers.forEach((header, index) => {
                    rowData[header] = row[index] || '';
                });

                if (rowData['级别']) {
                    bomData.push(rowData);
                }
            }

            // 分析配置类型
            const level1Items = bomData.filter(item => item['级别'] === '1');
            const configurations = level1Items.map(item => ({
                code: item['物件编号'],
                name: item['物件描述'],
                type: item['物件描述'].includes('高配') ? 'high' :
                      item['物件描述'].includes('低配') ? 'low' : 'other'
            }));

            return {
                fileName: fileName,
                modelCode: fileName.replace('.xlsx', ''),
                totalRecords: bomData.length,
                configurations: configurations,
                sampleRecords: bomData,
                levelStats: bomData.reduce((stats, item) => {
                    const level = item['级别'];
                    stats[level] = (stats[level] || 0) + 1;
                    return stats;
                }, {})
            };
        }

        // 解析产品工作簿
        function parseProductWorkbook(workbook, fileName) {
            // 尝试读取sheet2
            let sheetName = 'Sheet2';
            if (!workbook.SheetNames.includes(sheetName)) {
                sheetName = workbook.SheetNames[1] || workbook.SheetNames[0];
            }

            const worksheet = workbook.Sheets[sheetName];
            const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

            // 查找表头
            let headerRow = -1;
            let headers = [];

            for (let i = 0; i < Math.min(10, data.length); i++) {
                const row = data[i];
                if (row && row.some(cell => cell && cell.toString().includes('模型编号'))) {
                    headerRow = i;
                    headers = row.filter(cell => cell && cell.toString().trim() !== '');
                    break;
                }
            }

            if (headerRow === -1) {
                throw new Error(`文件 ${fileName} 未找到产品数据表头`);
            }

            // 提取产品数据
            const productData = [];
            for (let i = headerRow + 1; i < data.length; i++) {
                const row = data[i];
                if (!row || !row[0]) continue;

                const rowData = {};
                headers.forEach((header, index) => {
                    rowData[header] = row[index] || '';
                });

                if (rowData['模型编号']) {
                    productData.push(rowData);
                }
            }

            return {
                fileName: fileName,
                headers: headers,
                totalRecords: productData.length,
                sampleRecords: productData
            };
        }

        // 显示通知
        function showNotice(message, type = 'info') {
            // 移除现有通知
            const existingNotice = document.querySelector('.notice');
            if (existingNotice) {
                existingNotice.remove();
            }

            const notice = document.createElement('div');
            notice.className = 'notice';

            if (type === 'success') {
                notice.style.background = '#d4edda';
                notice.style.borderColor = '#c3e6cb';
                notice.style.color = '#155724';
            } else if (type === 'error') {
                notice.style.background = '#f8d7da';
                notice.style.borderColor = '#f5c6cb';
                notice.style.color = '#721c24';
            }

            notice.innerHTML = `<strong>${type === 'error' ? '错误' : type === 'success' ? '成功' : '提示'}:</strong> ${message}`;

            const container = document.querySelector('.container');
            container.insertBefore(notice, container.children[1]);

            // 5秒后自动移除
            setTimeout(() => {
                if (notice.parentNode) {
                    notice.remove();
                }
            }, 5000);
        }

        // 加载选中的方案
        function loadSolution() {
            const selectedSolution = document.getElementById('solutionSelect').value;
            if (!selectedSolution) {
                alert('请先选择一个方案');
                return;
            }

            const solution = allSolutionsData.solutions.find(s => s.modelCode === selectedSolution);
            if (!solution) {
                alert('未找到选中的方案数据');
                return;
            }

            currentSolutionData = solution.sampleRecords;
            currentSolution = selectedSolution;

            // 显示配置选择器
            buildConfigSelector(solution.configurations);

            // 应用产品刷新数据
            if (productRefreshData) {
                applyProductRefresh();
            }

            // 默认选择高配
            currentConfig = 'high';
            document.querySelector('input[name="config"][value="high"]')?.click();

            document.getElementById('solutionTabs').style.display = 'block';
            document.getElementById('stats').style.display = 'flex';
        }

        // 构建配置选择器
        function buildConfigSelector(configurations) {
            const configSelector = document.getElementById('configSelector');
            const configOptions = document.getElementById('configOptions');

            configOptions.innerHTML = '';

            configurations.forEach((config, index) => {
                const option = document.createElement('div');
                option.className = 'config-option';

                const radio = document.createElement('input');
                radio.type = 'radio';
                radio.name = 'config';
                radio.value = config.type;
                radio.id = `config_${config.type}`;
                radio.onchange = () => switchConfig(config.type);

                if (index === 0) radio.checked = true;

                const label = document.createElement('label');
                label.htmlFor = `config_${config.type}`;
                label.textContent = config.name;

                option.appendChild(radio);
                option.appendChild(label);
                configOptions.appendChild(option);
            });

            configSelector.style.display = 'block';
        }

        // 切换配置
        function switchConfig(configType) {
            currentConfig = configType;

            // 过滤当前配置的数据
            const configCode = allSolutionsData.solutions
                .find(s => s.modelCode === currentSolution)
                .configurations.find(c => c.type === configType)?.code;

            if (configCode) {
                filteredData = currentSolutionData.filter(item => {
                    // 如果是级别1，只显示当前配置
                    if (item['级别'] === '1') {
                        return item['物件编号'] === configCode;
                    }
                    // 其他级别需要判断是否属于当前配置的子项
                    return isUnderConfig(item, configCode);
                });
            } else {
                filteredData = [...currentSolutionData];
            }

            buildSolutionTabs();
            updateStats();
        }

        // 判断项目是否属于指定配置
        function isUnderConfig(item, configCode) {
            const itemIndex = currentSolutionData.findIndex(d => d['物件编号'] === item['物件编号']);
            const configIndex = currentSolutionData.findIndex(d => d['物件编号'] === configCode);

            if (itemIndex === -1 || configIndex === -1) return false;

            // 查找下一个级别1项目的位置
            let nextConfigIndex = currentSolutionData.length;
            for (let i = configIndex + 1; i < currentSolutionData.length; i++) {
                if (currentSolutionData[i]['级别'] === '1') {
                    nextConfigIndex = i;
                    break;
                }
            }

            return itemIndex > configIndex && itemIndex < nextConfigIndex;
        }

        // 应用产品刷新数据
        function applyProductRefresh() {
            if (!productRefreshData || !currentSolutionData) return;

            let updateCount = 0;

            // 遍历当前方案数据
            currentSolutionData.forEach(item => {
                const productCode = item['物件编号'];

                // 在刷新数据中查找匹配的产品
                const refreshItem = productRefreshData.sampleRecords.find(r =>
                    r['产品编号'] === productCode && r['模型编号'] === currentSolution
                );

                if (refreshItem) {
                    // 更新生命周期和描述
                    if (refreshItem['生命周期'] && refreshItem['生命周期'] !== item['物件的生命周期阶段']) {
                        item['物件的生命周期阶段'] = refreshItem['生命周期'];
                        updateCount++;
                    }

                    if (refreshItem['产品描述'] && refreshItem['产品描述'] !== item['物件描述']) {
                        item['物件描述'] = refreshItem['产品描述'];
                        updateCount++;
                    }
                }
            });

            if (updateCount > 0) {
                console.log(`应用产品刷新数据，更新了 ${updateCount} 个字段`);

                // 记录刷新操作
                changeLog.push({
                    timestamp: new Date().toISOString(),
                    operation: '数据刷新',
                    description: `从产品刷新数据更新了 ${updateCount} 个字段`,
                    updateCount: updateCount
                });
            }
        }

        // 构建方案页签
        function buildSolutionTabs() {
            if (!filteredData) return;

            // 按级别分组数据
            const groupedData = groupByLevel(filteredData);

            // 获取级别2的数据作为页签
            const level2Items = groupedData['2'] || [];

            const tabHeaders = document.getElementById('tabHeaders');
            tabHeaders.innerHTML = '';

            if (level2Items.length === 0) {
                document.getElementById('tabContent').innerHTML = '<div class="empty">该配置没有级别2分类</div>';
                return;
            }

            // 创建页签头
            level2Items.forEach((item, index) => {
                const tabHeader = document.createElement('div');
                tabHeader.className = `tab-header ${index === 0 ? 'active' : ''}`;
                tabHeader.textContent = item['物件描述'] || item['物件编号'];
                tabHeader.onclick = () => switchTab(index, item['物件编号']);
                tabHeaders.appendChild(tabHeader);
            });

            // 显示第一个页签的内容
            if (level2Items.length > 0) {
                switchTab(0, level2Items[0]['物件编号']);
            }
        }

        // 按级别分组数据
        function groupByLevel(data) {
            const grouped = {};
            data.forEach(item => {
                const level = item['级别'];
                if (!grouped[level]) {
                    grouped[level] = [];
                }
                grouped[level].push(item);
            });
            return grouped;
        }

        // 切换页签
        function switchTab(tabIndex, level2Code) {
            // 更新页签样式
            const tabHeaders = document.querySelectorAll('.tab-header');
            tabHeaders.forEach((header, index) => {
                header.classList.toggle('active', index === tabIndex);
            });

            // 显示对应的内容
            displayTabContent(level2Code);
        }

        // 显示页签内容
        function displayTabContent(level2Code) {
            const tabContent = document.getElementById('tabContent');

            // 获取该级别2下的所有级别3和级别4数据
            const level3Items = filteredData.filter(item =>
                item['级别'] === '3' && isChildOf(item, level2Code, filteredData)
            );

            if (level3Items.length === 0) {
                tabContent.innerHTML = '<div class="empty">该分类下没有子项目</div>';
                return;
            }

            let html = '';

            level3Items.forEach(level3Item => {
                const level4Items = filteredData.filter(item =>
                    item['级别'] === '4' && isChildOf(item, level3Item['物件编号'], filteredData)
                );

                html += `
                    <div class="category">
                        <div class="category-header" onclick="toggleCategory(this)">
                            <span>${level3Item['物件描述'] || level3Item['物件编号']}</span>
                            <span>▼</span>
                        </div>
                        <div class="category-content">
                `;

                if (level4Items.length === 0) {
                    html += '<div class="empty">该分类下没有产品</div>';
                } else {
                    level4Items.forEach(level4Item => {
                        html += createProductItemHTML(level4Item);
                    });
                }

                html += `
                        </div>
                    </div>
                `;
            });

            tabContent.innerHTML = html;
        }

        // 判断是否为子项目
        function isChildOf(childItem, parentCode, allData) {
            const childIndex = allData.findIndex(item => item['物件编号'] === childItem['物件编号']);
            const parentIndex = allData.findIndex(item => item['物件编号'] === parentCode);

            if (childIndex === -1 || parentIndex === -1) return false;

            // 查找在父项目之后、下一个同级或更高级项目之前的所有项目
            for (let i = parentIndex + 1; i < allData.length; i++) {
                if (allData[i]['物件编号'] === childItem['物件编号']) {
                    return true;
                }

                // 如果遇到同级或更高级的项目，说明已经超出了父项目的范围
                const currentLevel = parseInt(allData[i]['级别']);
                const parentLevel = parseInt(allData[parentIndex]['级别']);
                if (currentLevel <= parentLevel) {
                    break;
                }
            }

            return false;
        }

        // 创建产品项目HTML
        function createProductItemHTML(item) {
            const productCode = item['物件编号'];
            const productDesc = item['物件描述'] || '无描述';
            const lifecycle = item['物件的生命周期阶段'] || '未知';
            const remark = item['备注'] || '';
            const quantity = item['数量'] || '1';

            // 判断是否为实际产品（以数字开头，不是X开头）
            const isProduct = /^[0-9]/.test(productCode);

            return `
                <div class="product-item" data-code="${productCode}">
                    <div class="product-info">
                        <div class="product-code">${productCode}</div>
                        <div class="product-desc">${productDesc}</div>
                        <div class="product-meta">
                            数量: ${quantity} |
                            <span class="lifecycle-badge lifecycle-${lifecycle}">${lifecycle}</span>
                            ${remark ? ` | 备注: ${remark}` : ''}
                        </div>
                    </div>
                    ${isProduct ? `
                    <div class="product-actions">
                        <button onclick="editProduct('${productCode}')">编辑</button>
                        <button onclick="replaceProduct('${productCode}')" class="secondary">替换</button>
                        <button onclick="insertProduct('${productCode}', 'before')" class="secondary">向上插入</button>
                        <button onclick="insertProduct('${productCode}', 'after')" class="secondary">向下插入</button>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // 切换分类展开/收起
        function toggleCategory(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('span:last-child');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                arrow.textContent = '▼';
            } else {
                content.style.display = 'none';
                arrow.textContent = '▶';
            }
        }

        // 更新统计信息
        function updateStats() {
            if (!filteredData) return;

            const totalProducts = filteredData.length;
            const level4Products = filteredData.filter(item => item['级别'] === '4').length;
            const categories = new Set(filteredData.filter(item => item['级别'] === '3').map(item => item['物件编号'])).size;

            const configName = allSolutionsData.solutions
                .find(s => s.modelCode === currentSolution)
                ?.configurations.find(c => c.type === currentConfig)?.name || '未知';

            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('level4Products').textContent = level4Products;
            document.getElementById('totalCategories').textContent = categories;
            document.getElementById('currentConfig').textContent = configName.replace(/^(高配|低配)-/, '');
        }

        // 应用筛选
        function applyFilter() {
            if (!currentSolutionData) return;

            const lifecycleFilter = document.getElementById('lifecycleFilter').value;

            // 先按配置筛选
            switchConfig(currentConfig);

            // 再按生命周期筛选
            if (lifecycleFilter) {
                filteredData = filteredData.filter(item =>
                    item['物件的生命周期阶段'] === lifecycleFilter
                );
            }

            // 重新构建页签内容
            buildSolutionTabs();
            updateStats();
        }

        // 编辑产品
        function editProduct(productCode) {
            const product = currentSolutionData.find(item => item['物件编号'] === productCode);
            if (!product) return;

            document.getElementById('editProductCode').value = productCode;
            document.getElementById('editProductDesc').value = product['物件描述'] || '';
            document.getElementById('editLifecycle').value = product['物件的生命周期阶段'] || '量产';
            document.getElementById('editRemark').value = product['备注'] || '';

            document.getElementById('editModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 保存产品
        function saveProduct() {
            const productCode = document.getElementById('editProductCode').value;
            const productDesc = document.getElementById('editProductDesc').value;
            const lifecycle = document.getElementById('editLifecycle').value;
            const remark = document.getElementById('editRemark').value;

            const productIndex = currentSolutionData.findIndex(item => item['物件编号'] === productCode);
            if (productIndex === -1) return;

            // 记录变更
            const oldProduct = { ...currentSolutionData[productIndex] };

            // 更新产品信息
            currentSolutionData[productIndex]['物件描述'] = productDesc;
            currentSolutionData[productIndex]['物件的生命周期阶段'] = lifecycle;
            currentSolutionData[productIndex]['备注'] = remark;

            // 记录变更日志
            changeLog.push({
                timestamp: new Date().toISOString(),
                operation: '修改',
                productCode: productCode,
                oldData: oldProduct,
                newData: { ...currentSolutionData[productIndex] }
            });

            // 更新显示
            switchConfig(currentConfig);
            closeModal();

            showNotice('产品信息已更新', 'success');
        }

        // 删除产品
        function deleteProduct() {
            const productCode = document.getElementById('editProductCode').value;

            if (!confirm(`确定要删除产品 ${productCode} 吗？`)) return;

            const productIndex = currentSolutionData.findIndex(item => item['物件编号'] === productCode);
            if (productIndex === -1) return;

            // 记录变更
            const deletedProduct = { ...currentSolutionData[productIndex] };

            // 删除产品
            currentSolutionData.splice(productIndex, 1);

            // 记录变更日志
            changeLog.push({
                timestamp: new Date().toISOString(),
                operation: '删除',
                productCode: productCode,
                oldData: deletedProduct,
                newData: null
            });

            // 更新显示
            switchConfig(currentConfig);
            updateStats();
            closeModal();

            showNotice('产品已删除', 'success');
        }

        // 替换产品
        function replaceProduct(productCode) {
            const newProductCode = prompt(`请输入新的产品编号来替换 ${productCode}:`);
            if (!newProductCode || newProductCode === productCode) return;

            const productIndex = currentSolutionData.findIndex(item => item['物件编号'] === productCode);
            if (productIndex === -1) return;

            // 询问是否批量替换
            const batchReplace = confirm('是否要批量替换该物料在方案中的所有出现？');

            if (batchReplace) {
                // 批量替换
                const oldProduct = { ...currentSolutionData[productIndex] };
                let replaceCount = 0;

                currentSolutionData.forEach(item => {
                    if (item['物件编号'] === productCode) {
                        item['物件编号'] = newProductCode;
                        replaceCount++;
                    }
                });

                // 记录变更日志
                changeLog.push({
                    timestamp: new Date().toISOString(),
                    operation: '批量替换',
                    productCode: productCode,
                    newProductCode: newProductCode,
                    replaceCount: replaceCount,
                    oldData: oldProduct
                });

                showNotice(`已批量替换 ${replaceCount} 个产品编号`, 'success');
            } else {
                // 单个替换
                const oldProduct = { ...currentSolutionData[productIndex] };
                currentSolutionData[productIndex]['物件编号'] = newProductCode;

                // 记录变更日志
                changeLog.push({
                    timestamp: new Date().toISOString(),
                    operation: '替换',
                    productCode: productCode,
                    newProductCode: newProductCode,
                    oldData: oldProduct
                });

                showNotice('产品编号已替换', 'success');
            }

            // 更新显示
            switchConfig(currentConfig);
        }

        // 插入产品
        function insertProduct(productCode, position) {
            const newProductCode = prompt('请输入新产品编号:');
            if (!newProductCode) return;

            const newProductDesc = prompt('请输入新产品描述:');
            if (!newProductDesc) return;

            const productIndex = currentSolutionData.findIndex(item => item['物件编号'] === productCode);
            if (productIndex === -1) return;

            // 创建新产品对象
            const referenceProduct = currentSolutionData[productIndex];
            const newProduct = {
                '级别': referenceProduct['级别'],
                '物件编号': newProductCode,
                '物件描述': newProductDesc,
                '物件的生命周期阶段': '开发',
                '数量': '1',
                '备注': '',
                '物件类型': referenceProduct['物件类型'],
                // 复制其他必要字段
                ...Object.keys(referenceProduct).reduce((acc, key) => {
                    if (!['物件编号', '物件描述', '物件的生命周期阶段', '数量', '备注'].includes(key)) {
                        acc[key] = '';
                    }
                    return acc;
                }, {})
            };

            // 插入位置
            const insertIndex = position === 'before' ? productIndex : productIndex + 1;
            currentSolutionData.splice(insertIndex, 0, newProduct);

            // 记录变更日志
            changeLog.push({
                timestamp: new Date().toISOString(),
                operation: '插入',
                productCode: newProductCode,
                position: position,
                referenceCode: productCode,
                newData: newProduct
            });

            // 更新显示
            switchConfig(currentConfig);
            updateStats();

            showNotice(`新产品已${position === 'before' ? '向上' : '向下'}插入`, 'success');
        }

        // 刷新数据
        function refreshData() {
            if (currentSolution) {
                loadSolution();
                showNotice('数据已刷新', 'success');
            } else {
                loadSampleData();
                showNotice('方案列表已刷新', 'success');
            }
        }

        // 导出方案清单
        function exportSolution() {
            if (!currentSolutionData) {
                alert('请先加载方案数据');
                return;
            }

            // 创建CSV内容
            const headers = ['级别', '物件编号', '物件描述', '数量', '物件类型', '生命周期阶段', '备注'];
            let csvContent = headers.join(',') + '\n';

            currentSolutionData.forEach(item => {
                const row = headers.map(header => {
                    const value = item[header] || '';
                    return `"${value.toString().replace(/"/g, '""')}"`;
                });
                csvContent += row.join(',') + '\n';
            });

            // 下载文件
            downloadCSV(csvContent, `${currentSolution}_方案清单_${new Date().toISOString().split('T')[0]}.csv`);
        }

        // 导出变更记录
        function exportChanges() {
            if (changeLog.length === 0) {
                alert('没有变更记录可导出');
                return;
            }

            // 创建CSV内容
            const headers = ['时间', '操作', '产品编号', '新产品编号', '描述', '备注'];
            let csvContent = headers.join(',') + '\n';

            changeLog.forEach(change => {
                const row = [
                    change.timestamp,
                    change.operation,
                    change.productCode || '',
                    change.newProductCode || '',
                    change.newData ? change.newData['物件描述'] : (change.oldData ? change.oldData['物件描述'] : ''),
                    change.replaceCount ? `批量替换${change.replaceCount}个` : ''
                ];
                csvContent += row.map(cell => `"${cell.toString().replace(/"/g, '""')}"`).join(',') + '\n';
            });

            // 下载文件
            downloadCSV(csvContent, `变更记录_${new Date().toISOString().split('T')[0]}.csv`);
        }

        // 导出Excel
        function exportToExcel() {
            if (!currentSolutionData) {
                alert('请先加载方案数据');
                return;
            }

            try {
                // 创建工作簿
                const wb = XLSX.utils.book_new();

                // 按配置分组数据
                const configurations = allSolutionsData.solutions
                    .find(s => s.modelCode === currentSolution)?.configurations || [];

                configurations.forEach(config => {
                    const configData = currentSolutionData.filter(item => {
                        if (item['级别'] === '1') {
                            return item['物件编号'] === config.code;
                        }
                        return isUnderConfig(item, config.code);
                    });

                    if (configData.length > 0) {
                        const ws = XLSX.utils.json_to_sheet(configData);
                        XLSX.utils.book_append_sheet(wb, ws, config.name.substring(0, 31)); // Excel工作表名限制31字符
                    }
                });

                // 下载文件
                XLSX.writeFile(wb, `${currentSolution}_方案清单_${new Date().toISOString().split('T')[0]}.xlsx`);
                showNotice('Excel文件已导出', 'success');

            } catch (error) {
                console.error('导出Excel失败:', error);
                showNotice('导出Excel失败: ' + error.message, 'error');
            }
        }

        // 下载CSV文件
        function downloadCSV(content, filename) {
            const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
